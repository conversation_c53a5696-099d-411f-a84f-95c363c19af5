<script setup lang="ts" name="permissiondistribution">
import {reactive, ref, computed} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import departmentFavoriteComp from '@/components/common/department-favorite-comp.vue'
import {surplusDate, ReportsFlowStatusType, ReportsFlowStatus} from '@/define/statement.define'
import {GetPlanTaskProgress} from '@/api/ReportApi'
import DataPermissions from './components/DataPermissions.vue'
import YJModel from './components/YJModel.vue'
import YJProblem from './components/YJProblem.vue'
import QXCategory from './components/QXCategory.vue'
import QXAllocationBackup from './components/QXAllocationBackup.vue'
import QXOperationRecord from './components/QXOperationRecord.vue'
import TemporaryAuthorization from './components/TemporaryAuthorization.vue'

import OperatePermissions from './components/OperatePermissions.vue'

import AnalysisReportLabel from './components/AnalysisReportLabel.vue'
import AllocationRecord from './components/AllocationRecord.vue'
import AllocationReport from './components/AllocationReport.vue'
import MenuPermissions from './components/MenuPermissions.vue'
import AllocationExplanation from './components/AllocationExplanation.vue'
import PermissionDistributionChart from './components/PermissionDistributionChart.vue'
import OperationVideo from './components/OperationVideo.vue'
import LifeCycle from './components/LifeCycle.vue'
import PermissionVersion from './components/PermissionVersion.vue'
import OperatingManual from './components/OperatingManual.vue'
import ReminderContent from './components/ReminderContent.vue'
import ChangeLog from './components/ChangeLog.vue'
import ReceiveSettings from './components/ReceiveSettings.vue'
import VolumeLicense from './components/VolumeLicense.vue'
import PermissionRecord from './components/PermissionRecord.vue'
import PermissionCancel from './components/PermissionCancel.vue'
import PermissionUsageReport from './components/PermissionUsageReport.vue'
import PermissionTemplate from './components/PermissionTemplate.vue'
import PermissionComparison from './components/PermissionComparison.vue'
import PermissionComparisonReport from './components/PermissionComparisonReport.vue'
import PermissionOptimizationRule from './components/PermissionOptimizationRule.vue'
import PermissionChangeNotification from './components/PermissionChangeNotification.vue'
import PermissionApplicationReport from './components/PermissionApplicationReport.vue'
const router = useRouter()
const loading = ref(false)
const show = ref(false)
const showDataPermissions = ref(false)
const showOperatePermissions = ref(false)

const checkDepartmentList = ref([])
const defaultCheckedData = ref([])
const defaultCheckUserData = ref([])

const formProps = ref([{label: '任务名称', prop: 'name', type: 'text'}])
const form = ref({name: ''})
const nodeData = ref<any[]>([])
const optionSelect = [
	{label: '列表转换', value: '列表转换'},
	{label: '时间格式转换', value: '时间格式转换'},
	{label: '标准转换', value: '标准转换'},
	{label: '字典替换', value: '字典替换'},
	{label: '多字段标准转换', value: '多字段标准转换'},
]
const columns = [
	{label: '任务名称', prop: 'name'},
	{label: '填报范围', prop: 'fillingRange'},
	{label: '填报情况', prop: 'departmentReportingStatus'},
	{label: '截止时间', prop: 'endDate'},
	{label: '创建日期', prop: 'creationTime'},
	{label: '任务状态', prop: 'status'},
]

const tableRef = ref()
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams = reactive({
	name: '',
	skipCount: 0,
	maxResultCount: 10,
})
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onBeforeComplete = ({items, next}: any) => {
	const newData: any = []
	items.forEach((item: any) => {
		newData.push({
			...item,
			remindDays: surplusDate(item.fillingPeriodType, item.newToDays, item.endDate),
		})
	})
	next(newData)
}

const getPlanTaskProgress = () => {
	loading.value = true
	const tableData = tableRef.value?.getTableData()
	GetPlanTaskProgress(tableData.map((item: any) => item.id))
		.then((res: any) => {
			res.data.forEach((item: any) => {
				const task = tableData.find((x: any) => x.id === item.planTaskId)
				if (task) {
					task.departmentReportingStatus = item.reportTaskStatus
				}
			})
		})
		.catch((err: any) => {
			window.errMsg(err, '获取进度')
		})
		.finally(() => {
			loading.value = false
		})
}

const onTableButtonClick = ({btn, row}: any) => {
	show.value = true
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.name = form.value.name
}

const departmentListChange = () => {}
const activeIndex = ref(-1)
const handlCreate = () => {
	nodeData.value.push({
		id: generateUniqueRandomId(),
		node: '',
		departmentName: '',
	})
	activeIndex.value = nodeData.value.length - 1
}
const generateUniqueRandomId = (): number => {
	let newId: number
	const existingIds = nodeData.value.map((item) => item.id)
	do {
		newId = Math.floor(Math.random() * 1000)
	} while (existingIds.includes(newId))
	return newId
}
const nodeClick = (row: any) => {
	activeIndex.value = nodeData.value.indexOf(row)
}
const handlDelete = (row: any) => {
	ElMessageBox.confirm('请确认是否删除？删除后不可恢复！', '删除')
		.then(async (type) => {
			if (type === 'confirm') {
				nodeData.value = nodeData.value.filter((item: any) => item.id !== row.id)
				return ElMessage.success('删除成功')
			}
		})
		.catch((err) => {
			console.log(err)
		})
}
const checkDepartment = (row: any) => {
	console.log(row)
	return ElMessage.success('查验成功')
}

const showTableLabel = ref(false)
const isAllocationRecord = ref(false)
const isAllocationReport = ref(false)
const isMenuPermissions = ref(false)
const isAllocationExplanation = ref(false)
const isPermissionDistributionChart = ref(false)
const isOperationVideo = ref(false)
const isLifeCycle = ref(false)
const isPermissionVersion = ref(false)
const isOperatingManual = ref(false)
const isYJModel = ref(false)
const isYJProblem = ref(false)
const isReminderContent = ref(false)
const isQXCategory = ref(false)
const isQXAllocationBackup = ref(false)
const isChangeLog = ref(false)
const isQXOperationRecord = ref(false)
const isTemporaryAuthorization = ref(false)
const isReceiveSettings = ref(false)
const isVolumeLicense = ref(false)
const isPermissionRecord = ref(false)
const isPermissionCancel = ref(false)
const isPermissionUsageReport = ref(false)
const isPermissionTemplate = ref(false)
const isPermissionComparison = ref(false)
const isPermissionComparisonReport = ref(false)
const isPermissionOptimizationRule = ref(false)
const isPermissionChangeNotification = ref(false)
const isPermissionApplicationReport = ref(false)
const isPermissionCombined=ref(false)
const isPermissionCombineRecord=ref(false)
const isViewRecord=ref(false)

// 权限合并相关数据
const permissionCombineForm = reactive({
	mergePermission: '', // 合并权限（单选）
	toBeMergedPermissions: [] // 被合并权限（多选）
})

// 模拟权限数据
const permissionOptions = ref([
	{ label: '用户管理权限', value: 'user_manage' },
	{ label: '角色管理权限', value: 'role_manage' },
	{ label: '部门管理权限', value: 'dept_manage' },
	{ label: '系统配置权限', value: 'system_config' },
	{ label: '数据查看权限', value: 'data_view' },
	{ label: '数据编辑权限', value: 'data_edit' },
	{ label: '报表生成权限', value: 'report_generate' },
	{ label: '审批流程权限', value: 'approval_flow' },
	{ label: '文件上传权限', value: 'file_upload' },
	{ label: '日志查看权限', value: 'log_view' }
])

// 权限合并记录数据
const permissionCombineRecords = ref([
	{
		id: 1,
		mergePermission: '用户管理权限',
		toBeMergedPermissions: ['角色管理权限', '部门管理权限'],
		mergeTime: '2024-01-15 10:30:25'
	},
	{
		id: 2,
		mergePermission: '系统配置权限',
		toBeMergedPermissions: ['数据查看权限', '数据编辑权限', '报表生成权限'],
		mergeTime: '2024-01-14 14:20:18'
	},
	{
		id: 3,
		mergePermission: '审批流程权限',
		toBeMergedPermissions: ['文件上传权限'],
		mergeTime: '2024-01-13 09:15:42'
	}
])

// 权限合并记录表格列配置
const combineRecordColumns = [
	{ label: '序号', prop: 'index', width: '80px' },
	{ label: '合并权限', prop: 'mergePermission', width: '200px' },
	{ label: '被合并权限', prop: 'toBeMergedPermissions', width: '300px' },
	{ label: '合并时间', prop: 'mergeTime', width: '180px' },
	{ label: '操作', prop: 'operation', width: '150px' }
]

// 选中的记录
const selectedRecords = ref([])

// 筛选条件
const recordFilter = reactive({
	mergePermission: ''
})

// 当前查看的记录
const currentViewRecord = ref(null)

// 筛选后的记录
const filteredRecords = computed(() => {
	if (!recordFilter.mergePermission) {
		return permissionCombineRecords.value
	}
	return permissionCombineRecords.value.filter(record =>
		record.mergePermission === recordFilter.mergePermission
	)
})

// 权限合并方法
const handlePermissionMerge = () => {
	if (!permissionCombineForm.mergePermission) {
		ElMessage.warning('请选择合并权限')
		return
	}
	if (permissionCombineForm.toBeMergedPermissions.length === 0) {
		ElMessage.warning('请选择被合并权限')
		return
	}

	// 保存合并记录
	const mergePermissionLabel = permissionOptions.value.find(item => item.value === permissionCombineForm.mergePermission)?.label
	const toBeMergedLabels = permissionCombineForm.toBeMergedPermissions.map(value =>
		permissionOptions.value.find(item => item.value === value)?.label
	)

	const newRecord = {
		id: permissionCombineRecords.value.length + 1,
		mergePermission: mergePermissionLabel,
		toBeMergedPermissions: toBeMergedLabels,
		mergeTime: new Date().toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		}).replace(/\//g, '-')
	}

	permissionCombineRecords.value.unshift(newRecord)

	ElMessage.success('权限合并操作成功')
	// 重置表单
	permissionCombineForm.mergePermission = ''
	permissionCombineForm.toBeMergedPermissions = []
	isPermissionCombined.value = false
}

// 全部合并方法
const handleAllPermissionMerge = () => {
	if (!permissionCombineForm.mergePermission) {
		ElMessage.warning('请选择合并权限')
		return
	}

	// 保存全部合并记录
	const mergePermissionLabel = permissionOptions.value.find(item => item.value === permissionCombineForm.mergePermission)?.label
	const allOtherPermissions = permissionOptions.value
		.filter(item => item.value !== permissionCombineForm.mergePermission)
		.map(item => item.label)

	const newRecord = {
		id: permissionCombineRecords.value.length + 1,
		mergePermission: mergePermissionLabel,
		toBeMergedPermissions: allOtherPermissions,
		mergeTime: new Date().toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		}).replace(/\//g, '-')
	}

	permissionCombineRecords.value.unshift(newRecord)

	ElMessage.success('全部合并操作成功')
	// 重置表单
	permissionCombineForm.mergePermission = ''
	permissionCombineForm.toBeMergedPermissions = []
	isPermissionCombined.value = false
}

// 权限合并记录相关方法
const handleExportRecord = (record: any) => {
	// 创建导出数据
	const exportData = {
		合并权限: record.mergePermission,
		被合并权限: record.toBeMergedPermissions.join('、'),
		合并时间: record.mergeTime
	}

	// 模拟导出功能
	const dataStr = JSON.stringify(exportData, null, 2)
	const blob = new Blob([dataStr], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限合并记录_${record.mergePermission}_${record.mergeTime.replace(/[:\s]/g, '')}.json`
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)

	ElMessage.success(`导出记录：${record.mergePermission}`)
}

const handleViewRecord = (record: any) => {
	currentViewRecord.value = record
	isViewRecord.value = true
}

const handleBatchExport = () => {
	if (selectedRecords.value.length === 0) {
		ElMessage.warning('请选择要导出的记录')
		return
	}

	// 创建批量导出数据
	const exportData = selectedRecords.value.map((record: any) => ({
		合并权限: record.mergePermission,
		被合并权限: record.toBeMergedPermissions.join('、'),
		合并时间: record.mergeTime
	}))

	const dataStr = JSON.stringify(exportData, null, 2)
	const blob = new Blob([dataStr], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限合并记录_批量导出_${new Date().toISOString().slice(0, 10)}.json`
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)

	ElMessage.success(`批量导出 ${selectedRecords.value.length} 条记录`)
}

const handleExportAll = () => {
	if (permissionCombineRecords.value.length === 0) {
		ElMessage.warning('暂无记录可导出')
		return
	}

	// 创建全部导出数据
	const exportData = permissionCombineRecords.value.map((record: any) => ({
		合并权限: record.mergePermission,
		被合并权限: record.toBeMergedPermissions.join('、'),
		合并时间: record.mergeTime
	}))

	const dataStr = JSON.stringify(exportData, null, 2)
	const blob = new Blob([dataStr], { type: 'application/json' })
	const url = URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = `权限合并记录_全部导出_${new Date().toISOString().slice(0, 10)}.json`
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	URL.revokeObjectURL(url)

	ElMessage.success(`导出全部 ${permissionCombineRecords.value.length} 条记录`)
}

const handleSelectionChange = (selection: any) => {
	selectedRecords.value = selection
}

// 重置筛选条件
const resetFilter = () => {
	recordFilter.mergePermission = ''
}

const history = () => {
	router.push('/taskTransfer')
}
</script>
<template>
	<div class="permission-distribution">
		<Block
			title="自定义权限分配"
			:enable-fixed-height="true"
			@heightChanged="onBlockHeightChanged"
		>
			<template #topRight> </template>
			<template #expand>
				<div class="search">
					<Form
						v-model="form"
						:props="formProps"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
					<div>
						<el-button size="small" type="primary" @click="isReceiveSettings = true">
							数据管理岗接收设置
						</el-button>
						<el-button size="small" type="primary" @click="showDataPermissions = true">
							数据权限
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionVersion = true">
							权限版本
						</el-button>
						<el-button size="small" type="primary" @click="isAllocationRecord = true">
							分配记录
						</el-button>
						<el-button size="small" type="primary" @click="isAllocationReport = true">
							分配报告
						</el-button>
						<el-button size="small" type="primary" @click="isOperatingManual = true">
							权限操作手册
						</el-button>
						<el-button size="small" type="primary" @click="isOperationVideo = true">
							权限操作视频
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionUsageReport=true">
							权限运用报告
						</el-button>
						<el-button size="small" type="primary" @click="showTableLabel = true">
							赋权标签
						</el-button>
						<el-button size="small" type="primary" @click="isMenuPermissions = true">
							功能菜单权限
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionRecord=true">
							权限修改记录
						</el-button>
						<el-button size="small" type="primary" @click="isAllocationExplanation = true">
							权限分配说明
						</el-button>
						<el-button size="small" type="primary" @click="isLifeCycle = true">
							权限使用周期
						</el-button>
						
						<el-button size="small" type="primary" @click="isReminderContent = true">
							权限提醒内容
						</el-button>
						<el-button size="small" type="primary" @click="showOperatePermissions = true">
							操作权限
						</el-button>
						<el-button size="small" type="primary" @click="isYJModel = true">
							权限预警模型
						</el-button>
					
					</div>
					<div style="margin-top: 10px">
						<el-button size="small" type="primary" @click="isYJProblem = true">
							预警问题
						</el-button>
						<el-button size="small" type="primary" @click="isChangeLog = true">
							权限变更日志
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionDistributionChart = true">
							权限分布图表
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionTemplate=true">
							权限分配模板
						</el-button>
						<el-button size="small" type="primary" @click="isQXCategory = true">
							权限类别
						</el-button>
						<el-button size="small" type="primary" @click="isQXAllocationBackup = true">
							权限分配备份
						</el-button>
						<el-button size="small" type="primary" @click="isQXOperationRecord = true">
							权限操作纪录
						</el-button>
						<el-button size="small" type="primary" @click="isTemporaryAuthorization = true">
							临时授权
						</el-button>
						<el-button size="small" type="primary" @click="isVolumeLicense = true">
							批量授权
						</el-button>
						<el-button size="small" type="primary" @click="history">
							转派历史
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionCancel=true">
							取消授权
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionComparison=true">
							权限比对
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionComparisonReport=true">
							权限比对报告
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionOptimizationRule=true">
							权限优化规则
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionChangeNotification=true">
							权限变更通知
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionApplicationReport=true">
							权限应用报告
						</el-button>
					
					</div>
					<div style="margin-top: 10px">
						<el-button size="small" type="primary" @click="isPermissionCombined=true">
							权限合并
						</el-button>
						<el-button size="small" type="primary" @click="isPermissionCombineRecord=true">
							权限合并记录
						</el-button>
					</div>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/filling/plan-task"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:height="tableHeight"
				:auto-height="true"
				:enable-own-button="false"
				:buttons="[{type: 'primary', label: '权限分发', code: 'permissionDistribution'}]"
				@click-button="onTableButtonClick"
				@before-complete="onBeforeComplete"
				@completed="
					() => {
						pagination.total = tableRef?.getTotal()
						getPlanTaskProgress()
					}
				"
			>
				<template #departmentReportingStatus="scoped">
					<LoadingTransition v-if="loading"></LoadingTransition>
					<template v-else>
						{{ scoped.row.departmentReportingStatus || '-' }}
					</template>
				</template>
				<template #endDate="scope">
					<template v-if="scope.row.status === 9">已完结</template>
					<template v-else-if="scope.row.status === 8">已手动终止</template>
					<template v-else-if="scope.row.status === 10">时间已截止</template>
					<template v-else>
						<template v-if="scope.row.remindDays === false">
							<span text="gray">时间已截止</span>
						</template>
						<template v-else>
							<span
								v-if="scope.row.remindDays > 7"
								style="color: #20a162"
								class="df aic"
							>
								<el-icon style="color: #20a162">
									<WarningFilled />
								</el-icon>
								剩余{{ scope.row.remindDays }}天
							</span>
							<span
								v-if="scope.row.remindDays <= 7 && scope.row.remindDays > 3"
								class="df aic"
								style="color: #ffaa18"
							>
								<el-icon style="color: #ffaa18">
									<WarningFilled />
								</el-icon>
								剩余{{ scope.row.remindDays }}天
							</span>
							<span
								v-if="scope.row.remindDays <= 3"
								class="df aic"
								style="color: #20a162"
							>
								<el-icon style="color: #a61b29">
									<WarningFilled />
								</el-icon>
								剩余{{ scope.row.remindDays }}天
							</span>
						</template>
					</template>

					<!-- {{ scope.rowData.remindDays }} -->
				</template>
				<template #status="scoped">
					<el-tag :type="ReportsFlowStatusType(scoped.row.status)">
						{{
							ReportsFlowStatus.find(
								(f) => f.value === scoped.row.status && f.type === 'plan'
							)?.label || '-'
						}}
					</el-tag>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>
		<DataPermissions v-model="showDataPermissions" title="数据权限" width="1100" />
		<OperatePermissions v-model="showOperatePermissions" title="操作权限" width="1100" />
		<YJModel v-model="isYJModel" title="权限预警模型管理" width="1100" />
		<YJProblem v-model="isYJProblem" title="预警问题" width="1100" />
		<QXCategory v-model="isQXCategory" title="权限类别" width="1100" />
		<QXAllocationBackup v-model="isQXAllocationBackup" title="权限分配备份管理" width="1100" />
		<QXOperationRecord v-model="isQXOperationRecord" title="权限操作纪录" width="1100" />
		<TemporaryAuthorization v-model="isTemporaryAuthorization" title="临时授权" width="1100" />
		<AnalysisReportLabel v-model="showTableLabel" title="赋权标签" width="1100" />
		<AllocationRecord v-model="isAllocationRecord" title="分配记录" width="1100" />
		<AllocationReport v-model="isAllocationReport" title="分配报告" width="1100" />
		<MenuPermissions v-model="isMenuPermissions" title="功能菜单权限" width="1100" />
		<AllocationExplanation
			v-model="isAllocationExplanation"
			title="权限分配说明"
			width="1100"
		/>
		<PermissionDistributionChart
			v-model="isPermissionDistributionChart"
			title="权限分布图表"
			width="1100"
		/>

		<OperationVideo v-model="isOperationVideo" title="权限操作视频" width="1100" />
		<LifeCycle v-model="isLifeCycle" title="权限使用周期" width="1100" />
		<PermissionVersion v-model="isPermissionVersion" title="权限版本" width="1100" />
		<OperatingManual v-model="isOperatingManual" title="操作手册" width="1100" />
		<ReminderContent v-model="isReminderContent" title="提醒内容" width="1100" />
		<ChangeLog v-model="isChangeLog" title="变更日志权限" width="1100" />
		<ReceiveSettings v-model="isReceiveSettings" title="数据管理岗" width="800" />
		<VolumeLicense v-model="isVolumeLicense" title="批量授权" width="600" />
		<PermissionRecord v-model="isPermissionRecord" title="权限修改记录" width="1100" />
		<PermissionCancel v-model="isPermissionCancel" title="取消授权" width="1100" />
		<PermissionUsageReport v-model="isPermissionUsageReport" title="权限运用报告" width="1100" />
		<PermissionTemplate v-model="isPermissionTemplate" title="权限分配模板" width="1200" />
		<PermissionComparison v-model="isPermissionComparison" title="权限比对" width="1200" />
		<PermissionComparisonReport v-model="isPermissionComparisonReport" title="权限比对报告" width="1200" />
		<PermissionOptimizationRule v-model="isPermissionOptimizationRule" title="权限优化规则" width="1200" />
		<PermissionChangeNotification v-model="isPermissionChangeNotification" title="权限变更通知" width="1200" />
		<PermissionApplicationReport v-model="isPermissionApplicationReport" title="权限应用报告" width="1200" />

		<!-- 权限合并对话框 -->
		<Dialog
			v-model="isPermissionCombined"
			title="权限合并"
			width="600"
			:destroy-on-close="true"
		>
			<div class="permission-merge-form">
				<el-form :model="permissionCombineForm" label-width="120px">
					<el-form-item label="合并权限">
						<el-select
							v-model="permissionCombineForm.mergePermission"
							placeholder="请选择权限（单选）"
							style="width: 100%"
						>
							<el-option
								v-for="item in permissionOptions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="被合并权限">
						<el-select
							v-model="permissionCombineForm.toBeMergedPermissions"
							placeholder="请选择权限（多选）"
							multiple
							style="width: 100%"
						>
							<el-option
								v-for="item in permissionOptions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
								:disabled="item.value === permissionCombineForm.mergePermission"
							/>
						</el-select>
					</el-form-item>
				</el-form>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="isPermissionCombined = false">取消</el-button>
					<el-button type="primary" @click="handleAllPermissionMerge">全部合并</el-button>
					<el-button type="primary" @click="handlePermissionMerge">权限合并</el-button>
				</div>
			</template>
		</Dialog>

		<!-- 权限合并记录对话框 -->
		<Dialog
			v-model="isPermissionCombineRecord"
			title="权限合并记录"
			width="1000"
			:destroy-on-close="true"
		>
			<div class="permission-record-container">
				<!-- 筛选条件 -->
				<div class="record-filter">
					<el-form :model="recordFilter" inline>
						<el-form-item label="合并权限">
							<el-select
								v-model="recordFilter.mergePermission"
								placeholder="请选择合并权限"
								clearable
								style="width: 200px"
							>
								<el-option
									v-for="item in permissionOptions"
									:key="item.value"
									:label="item.label"
									:value="item.label"
								/>
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button @click="resetFilter">重置</el-button>
						</el-form-item>
					</el-form>
				</div>

				<!-- 操作按钮 -->
				<div class="record-actions">
					<el-button type="primary" @click="handleBatchExport">批量导出</el-button>
					<el-button type="primary" @click="handleExportAll">导出全部</el-button>
				</div>

				<!-- 记录表格 -->
				<el-table
					:data="filteredRecords"
					style="width: 100%; margin-top: 16px"
					@selection-change="handleSelectionChange"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column label="序号" width="80" type="index" :index="1" />
					<el-table-column label="合并权限" prop="mergePermission" width="200" />
					<el-table-column label="被合并权限" prop="toBeMergedPermissions" width="300">
						<template #default="scope">
							<el-tag
								v-for="(permission, index) in scope.row.toBeMergedPermissions"
								:key="index"
								size="small"
								style="margin-right: 4px; margin-bottom: 4px"
							>
								{{ permission }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="合并时间" prop="mergeTime" width="180" />
					<el-table-column label="操作" width="150">
						<template #default="scope">
							<el-button
								type="text"
								size="small"
								@click="handleExportRecord(scope.row)"
							>
								导出
							</el-button>
							<el-button
								type="text"
								size="small"
								@click="handleViewRecord(scope.row)"
							>
								查看
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</Dialog>

		<!-- 查看记录详情对话框 -->
		<Dialog
			v-model="isViewRecord"
			title="查看权限合并记录"
			width="600"
			:destroy-on-close="true"
		>
			<div v-if="currentViewRecord" class="view-record-detail">
				<el-descriptions :column="1" border>
					<el-descriptions-item label="记录ID">
						{{ currentViewRecord.id }}
					</el-descriptions-item>
					<el-descriptions-item label="合并权限">
						<el-tag type="primary">{{ currentViewRecord.mergePermission }}</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="被合并权限">
						<div class="merged-permissions">
							<el-tag
								v-for="(permission, index) in currentViewRecord.toBeMergedPermissions"
								:key="index"
								size="small"
								style="margin-right: 8px; margin-bottom: 8px"
							>
								{{ permission }}
							</el-tag>
						</div>
					</el-descriptions-item>
					<el-descriptions-item label="合并时间">
						{{ currentViewRecord.mergeTime }}
					</el-descriptions-item>
					<el-descriptions-item label="被合并权限数量">
						{{ currentViewRecord.toBeMergedPermissions.length }} 个
					</el-descriptions-item>
				</el-descriptions>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="isViewRecord = false">关闭</el-button>
					<el-button type="primary" @click="handleExportRecord(currentViewRecord)">导出此记录</el-button>
				</div>
			</template>
		</Dialog>

		<Dialog
			v-model="show"
			title="权限分发"
			:destroy-on-close="true"
			@click-confirm="
				() => {
					ElMessage.success('分配成功')
					show = false
				}
			"
		>
			<FormItem
				:items="[
					{
						label: '填报部门/人员',
						prop: 'treeSelect',
						type: 'treeSelect',
						labelWidth: 110,
					},
					{
						label: '授权有效期',
						prop: 'select',
						type: 'select',
						labelWidth: 110,
						options: optionSelect,
					},
					{
						label: '填报节点部门',
						prop: 'node',
						type: '',
						labelWidth: 110,
					},
				]"
			>
				<template #form-treeSelect>
					<departmentFavoriteComp
						placeholder="请选择"
						:type="'modal'"
						:data="checkDepartmentList"
						:defaultCheckedData="defaultCheckedData"
						:defaultCheckUserData="defaultCheckUserData"
						@change="departmentListChange"
					></departmentFavoriteComp>
				</template>
				<template #form-node>
					<el-table :data="nodeData" @row-click="nodeClick">
						<el-table-column prop="node" label="节点">
							<template #default="scope">
								<el-select
									v-if="activeIndex === scope.$index"
									placeholder="请选择节点"
									v-model="scope.row.node"
									size="small"
								>
									<el-option label="节点1" value="节点1" />
									<el-option label="节点2" value="节点2" />
									<el-option label="节点3" value="节点3" />
								</el-select>
								<span v-else>{{ scope.row.node }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="departmentName" label="部门名称">
							<template #default="scope">
								<el-input
									v-if="activeIndex === scope.$index"
									v-model="scope.row.departmentName"
									placeholder="请输入部门名称"
									size="small"
								/>
								<span v-else>{{ scope.row.departmentName }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="operation" label="删除">
							<template #default="scope">
								<el-button type="text" @click="activeIndex = scope.$index"
									>编辑</el-button
								>
								<el-button type="text" @click="handlDelete(scope.row)"
									>删除</el-button
								>
								<el-button type="text" @click="checkDepartment(scope.row)"
									>查验</el-button
								>
							</template>
						</el-table-column>
					</el-table>
					<div class="add-btn" @click="handlCreate">+新增一行</div>
				</template>
			</FormItem>
		</Dialog>
	</div>
</template>
<route>
    {
		meta: {
			title: '自定义权限分配',
		},
	}
</route>
<style scoped lang="scss">
.search {
	min-height: 100px;
	// line-height: 1.5;
}
.add-btn {
	width: 100%;
	height: 30px;
	text-align: center;
	line-height: 30px;
	border: 1px solid #ebeef5;
	border-top: none;
	color: #1764ce;
	cursor: pointer;
}

.permission-merge-form {
	padding: 20px 0;

	:deep(.el-form-item) {
		margin-bottom: 24px;
	}

	:deep(.el-select) {
		width: 100%;
	}
}

.dialog-footer {
	text-align: right;

	.el-button {
		margin-left: 10px;
	}
}

.permission-record-container {
	.record-filter {
		margin-bottom: 16px;
		padding: 16px;
		background-color: #f5f7fa;
		border-radius: 4px;
	}

	.record-actions {
		display: flex;
		justify-content: flex-end;
		gap: 12px;
		margin-bottom: 16px;
	}

	:deep(.el-table) {
		.el-tag {
			margin-right: 4px;
			margin-bottom: 4px;
		}
	}
}

.view-record-detail {
	.merged-permissions {
		.el-tag {
			margin-right: 8px;
			margin-bottom: 8px;
		}
	}
}
</style>
